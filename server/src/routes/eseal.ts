import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';
import { BeacukaiApiService } from '../services/BeacukaiApiService';
import { randomUUID } from 'crypto';
import { auth } from '../auth';

const prisma = new PrismaClient();
const beacukaiService = new BeacukaiApiService(prisma);

export const esealRoutes = new Hono()

// Get E-Seal detail by ID
.get('/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const esealDetail = await prisma.eSeal.findUnique({
      where: { id },
      include: {
        user: true,
        dokumen: true,
        trackingLogs: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });

    if (!esealDetail) {
      return c.json({ success: false, message: 'E-Seal tidak ditemukan' }, 404);
    }

    // Transform data untuk frontend
    const transformedData = {
      id: esealDetail.id,
      noEseal: esealDetail.noEseal,
      noImei: esealDetail.noImei,
      status: esealDetail.status,
      idVendor: esealDetail.idVendor,
      merk: esealDetail.merk || '',
      model: esealDetail.model || '',
      tipe: esealDetail.tipe || '',

      // Informasi Pengiriman
      nomorAJU: esealDetail.nomorAJU || '',
      kodeDokumen: esealDetail.dokumen?.[0]?.kodeDokumen || 'PLP',

      // Informasi Kontainer
      noKontainer: esealDetail.noKontainer || '',
      jnsKontainer: esealDetail.jnsKontainer || '',
      ukKontainer: esealDetail.ukKontainer || '',

      // Informasi Kendaraan & Driver
      noPolisi: esealDetail.noPolisi || '',
      namaDriver: esealDetail.namaDriver || '',
      nomorTeleponDriver: esealDetail.nomorTeleponDriver || '',

      // Informasi Lokasi
      alamatAsal: esealDetail.alamatAsal || '',
      alamatTujuan: esealDetail.alamatTujuan || '',
      latitudeAsal: esealDetail.latitudeAsal || '',
      longitudeAsal: esealDetail.longitudeAsal || '',
      latitudeTujuan: esealDetail.latitudeTujuan || '',
      longitudeTujuan: esealDetail.longitudeTujuan || '',

      // Log Activities dari tracking logs
      activities: esealDetail.trackingLogs?.map((log: any) => ({
        id: log.id,
        timestamp: log.createdAt.toISOString(),
        activity: `Location update: ${log.address || 'Unknown location'} (${log.latitude}, ${log.longitude})`,
        status: log.event || 'TRACKING'
      })) || []
    };

    return c.json({
      success: true,
      data: transformedData
    });
  } catch (error) {
    console.error('Error fetching E-Seal detail:', error);
    return c.json({
      success: false,
      message: 'Gagal mengambil detail E-Seal'
    }, 500);
  }
});

// Get all E-Seals with pagination and search
esealRoutes.get('/', async (c) => {
  try {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search") || "";
    const organizationSlug = c.req.query("organizationSlug");

    const skip = (page - 1) * limit;

    // Get current user session for filtering
    const session = await auth.api.getSession({ headers: c.req.header() });
    if (!session?.user) {
      return c.json({
        success: false,
        error: 'User authentication required'
      }, 401);
    }

    const user = session.user as any;
    const userRole = user.role;

    // Build where clause for search
    let where: any = search ? {
      OR: [
        { noEseal: { contains: search, mode: 'insensitive' as const } },
        { noImei: { contains: search, mode: 'insensitive' as const } },
        { idVendor: { contains: search, mode: 'insensitive' as const } },
        { merk: { contains: search, mode: 'insensitive' as const } },
        { model: { contains: search, mode: 'insensitive' as const } },
        { noPolisi: { contains: search, mode: 'insensitive' as const } },
        { namaDriver: { contains: search, mode: 'insensitive' as const } },
      ]
    } : {};

    // Filter by organization for non-superadmin users
    if (userRole !== 'superadmin') {
      if (organizationSlug) {
        const org = await prisma.organization.findUnique({
          where: { slug: organizationSlug },
        });
        if (org) {
          where.organizationId = org.id;
        } else {
          // If slug is provided but not found, return empty
          return c.json({
            success: true,
            data: { data: [], total: 0, page, limit }
          });
        }
      } else {
        const activeOrgId = session.activeOrganizationId;
        if (activeOrgId) {
          where.organizationId = activeOrgId;
        } else {
          // If no active organization and no slug, return empty
          return c.json({
            success: true,
            data: { data: [], total: 0, page, limit }
          });
        }
      }
    }

    const [eseals, total] = await Promise.all([
      prisma.eSeal.findMany({
        where,
        skip,
        take: limit,
        include: {
          dokumen: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.eSeal.count({ where })
    ]);

    return c.json({
      success: true,
      data: {
        data: eseals,
        total,
        page,
        limit
      }
    });
  } catch (error) {
    console.error('Error getting E-Seals:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get E-Seals',
    }, 500);
  }
})



// Create new E-Seal
.post('/', async (c) => {
  try {
    const body = await c.req.json();

    console.log('Creating E-Seal with data:', JSON.stringify(body, null, 2));

    
    // 2. Get current user ID from session
    const session = await auth.api.getSession({ headers: c.req.header() });
    if (!session?.user) {
      return c.json({
        success: false,
        error: 'User authentication required'
      }, 401);
    }
    const userId = session.user.id;

    // Validate required fields (excluding userId since we get it from auth)
    const requiredFields = [
      'noEseal', 'noImei', 'idVendor', 'alamatAsal', 'alamatTujuan',
      'lokasiAsal', 'lokasiTujuan', 'latitudeAsal', 'longitudeAsal',
      'latitudeTujuan', 'longitudeTujuan', 'noPolisi', 'ukKontainer',
      'jnsKontainer', 'noKontainer', 'namaDriver', 'nomorTeleponDriver',
      'organizationId'
    ];

    const missingFields = requiredFields.filter(field => !body[field] || body[field] === '');
    if (missingFields.length > 0) {
      console.log('Missing fields:', missingFields);
      console.log('Field values:', missingFields.map(field => `${field}: "${body[field]}"`));
      return c.json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`,
      }, 400);
    }

    // Check if E-Seal with same noEseal or noImei already exists
    const existingESeal = await prisma.eSeal.findFirst({
      where: {
        OR: [
          { noEseal: body.noEseal },
          { noImei: body.noImei }
        ]
      }
    });

    if (existingESeal) {
      console.log('E-Seal already exists, updating with new setup data:', {
        existing: {
          id: existingESeal.id,
          noEseal: existingESeal.noEseal,
          noImei: existingESeal.noImei
        },
        attempted: {
          noEseal: body.noEseal,
          noImei: body.noImei
        }
      });

      // Update existing E-Seal with new setup data
      const updatedESeal = await prisma.eSeal.update({
        where: { id: existingESeal.id },
        data: {
          noEseal: body.noEseal,
          noImei: body.noImei,
          idVendor: body.idVendor,
          merk: body.merk || '',
          model: body.model || '',
          tipe: body.tipe || '',
          alamatAsal: body.alamatAsal,
          alamatTujuan: body.alamatTujuan,
          lokasiAsal: body.lokasiAsal,
          lokasiTujuan: body.lokasiTujuan,
          latitudeAsal: body.latitudeAsal,
          longitudeAsal: body.longitudeAsal,
          latitudeTujuan: body.latitudeTujuan,
          longitudeTujuan: body.longitudeTujuan,
          noPolisi: body.noPolisi,
          ukKontainer: body.ukKontainer,
          jnsKontainer: body.jnsKontainer,
          noKontainer: body.noKontainer,
          namaDriver: body.namaDriver,
          nomorTeleponDriver: body.nomorTeleponDriver,
          nomorAJU: body.nomorAJU,
          statusValidasi: body.statusValidasi || 'PENDING',
          userId: userId,
          organizationId: body.organizationId,
          // Reset sync status for update
          isSync: false,
          beacukaiResponseLog: undefined,
          beacukaiId: null,
          // Update documents - delete old ones and create new ones
           dokumen: {
             deleteMany: {}, // Delete all existing documents
             create: (body.dokumen || []).map((doc: any) => ({
               id: randomUUID(),
               jenisMuat: doc.jenisMuat,
               jumlahKontainer: doc.jumlahKontainer,
               kodeDokumen: doc.kodeDokumen,
               kodeKantor: doc.kodeKantor,
               nomorAju: doc.nomorAju,
               nomorDokumen: doc.nomorDokumen,
               tanggalDokumen: doc.tanggalDokumen
             }))
           }
        },
        include: {
          dokumen: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      // Try to sync with Beacukai API using direct endpoint
      try {
        console.log('🔄 Syncing updated E-Seal with Beacukai API...');
        
        // Get valid session with JWT token
        const session = await beacukaiService['sessionService'].getValidSession();
        const jwtToken = (session as any).jwtToken;

        if (!jwtToken) {
          throw new Error('No JWT token available');
        }

        // Direct API call to Beacukai with proper token
        const apiUrl = 'https://apisdev-gw.beacukai.go.id/tracking-eseal/eseal/add';
        const payload = {
          idVendor: body.idVendor,
          merk: body.merk || '',
          model: body.model || '',
          noImei: body.noImei,
          tipe: body.tipe || '',
          token: body.token || "919253c8-d0e1-4780-89d0-e91f77e89855" // Use provided token or default
        };

        console.log('🌐 Calling Beacukai API:', apiUrl);
        console.log('📦 Payload:', payload);

        const headers: Record<string, string> = {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'E-Seal-Monitor/1.0'
        };

        // Add cookies if available
        if (session.cookies && session.cookies.length > 0) {
          headers['Cookie'] = session.cookies.join('; ');
        }

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(payload)
        });

        console.log(`📡 Beacukai API Response: ${response.status} ${response.statusText}`);

        let responseData: any;
        const contentType = response.headers.get('content-type');

        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json();
        } else {
          responseData = await response.text();
        }

        console.log('📄 Beacukai API Response Data:', responseData);

        // Check if Beacukai API response indicates success
        const isBeacukaiSuccess = response.ok && 
          (responseData.status === 'success' || 
           responseData.status === 'Valid' || 
           responseData.code === 200 ||
           (responseData.beacukaiResponse && responseData.beacukaiResponse.status === 'success'));

        // Update E-Seal with sync result
        await prisma.eSeal.update({
          where: { id: existingESeal.id },
          data: {
            isSync: isBeacukaiSuccess,
            beacukaiResponseLog: responseData as any,
            beacukaiId: responseData.item?.idEseal || null,
            idEseal: responseData.item?.idEseal || null
          }
        });

        console.log('✅ E-Seal sync result:', responseData);
        console.log('🔄 Sync status:', isBeacukaiSuccess ? 'SUCCESS' : 'FAILED');
        
      } catch (syncError) {
        console.error('❌ Error syncing E-Seal with Beacukai:', syncError);
        // Don't fail the request, just log the error
      }

      return c.json({
        success: true,
        data: updatedESeal,
        message: 'E-Seal updated successfully with new setup data',
      });
    }

    // Create E-Seal with documents (initially not synced)
    const eseal = await prisma.eSeal.create({
      data: {
           id: randomUUID(),
           noEseal: body.noEseal,
           noImei: body.noImei,
           idVendor: body.idVendor,
           merk: body.merk,
           model: body.model,
           tipe: body.tipe,
           alamatAsal: body.alamatAsal,
           alamatTujuan: body.alamatTujuan,
           lokasiAsal: body.lokasiAsal,
           lokasiTujuan: body.lokasiTujuan,
           latitudeAsal: body.latitudeAsal,
           longitudeAsal: body.longitudeAsal,
           latitudeTujuan: body.latitudeTujuan,
           longitudeTujuan: body.longitudeTujuan,
           noPolisi: body.noPolisi,
           ukKontainer: body.ukKontainer,
           jnsKontainer: body.jnsKontainer,
           noKontainer: body.noKontainer,
           namaDriver: body.namaDriver,
           nomorTeleponDriver: body.nomorTeleponDriver,
           nomorAJU: body.nomorAJU,
           userId: userId,
           organizationId: body.organizationId,
           isSync: false, // Initially not synced
           createdAt: new Date(),
            updatedAt: new Date(),
          dokumen: {
           create: (body.dokumen || []).map((doc: any) => ({
             id: randomUUID(),
             jenisMuat: doc.jenisMuat,
             jumlahKontainer: doc.jumlahKontainer,
             kodeDokumen: doc.kodeDokumen,
             kodeKantor: doc.kodeKantor,
             nomorAju: doc.nomorAju,
             nomorDokumen: doc.nomorDokumen,
             tanggalDokumen: doc.tanggalDokumen
           }))
         }
      },
      include: {
        dokumen: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    // Try to sync with Beacukai API using direct endpoint
    try {
      console.log('🔄 Syncing new E-Seal with Beacukai API...');
      
      // Get valid session with JWT token
      const session = await beacukaiService['sessionService'].getValidSession();
      const jwtToken = (session as any).jwtToken;

      if (!jwtToken) {
        throw new Error('No JWT token available');
      }

      // Direct API call to Beacukai with proper token
      const apiUrl = 'https://apisdev-gw.beacukai.go.id/tracking-eseal/eseal/add';
              const payload = {
          idVendor: body.idVendor,
          merk: body.merk || '',
          model: body.model || '',
          noImei: body.noImei,
          tipe: body.tipe || '',
          token: body.token || "919253c8-d0e1-4780-89d0-e91f77e89855" // Use provided token or default
        };

      console.log('🌐 Calling Beacukai API:', apiUrl);
      console.log('📦 Payload:', payload);

      const headers: Record<string, string> = {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'E-Seal-Monitor/1.0'
      };

      // Add cookies if available
      if (session.cookies && session.cookies.length > 0) {
        headers['Cookie'] = session.cookies.join('; ');
      }

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      });

      console.log(`📡 Beacukai API Response: ${response.status} ${response.statusText}`);

      let responseData: any;
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      console.log('📄 Beacukai API Response Data:', responseData);

      // Check if Beacukai API response indicates success
      const isBeacukaiSuccess = response.ok && 
        (responseData.status === 'success' || 
         responseData.status === 'Valid' || 
         responseData.code === 200 ||
         (responseData.beacukaiResponse && responseData.beacukaiResponse.status === 'success')) &&
        !!responseData.item?.idEseal; // Must have idEseal to be considered successful

      console.log('🔄 Success check:', {
        responseOk: response.ok,
        status: responseData.status,
        code: responseData.code,
        hasIdEseal: !!responseData.item?.idEseal,
        idEseal: responseData.item?.idEseal,
        isSuccess: isBeacukaiSuccess
      });

      // Log the request to database (save all logs - success or error)
      try {
        await prisma.requestLog.create({
          data: {
            id: randomUUID(),
            requestUrl: apiUrl,
            requestBody: payload,
            requestHeader: headers,
            status: response.status,
            responseBody: responseData,
            isSync: Boolean(isBeacukaiSuccess) // Log sync status
          }
        });
        console.log('✅ Request logged to database');
      } catch (logError) {
        console.error('Failed to log request:', logError);
      }

      // Update E-Seal with sync result
      await prisma.eSeal.update({
        where: { id: eseal.id },
        data: {
          isSync: Boolean(isBeacukaiSuccess),
          beacukaiResponseLog: responseData as any,
          beacukaiId: responseData.item?.idEseal || null,
          idEseal: responseData.item?.idEseal || null
        }
      });

      console.log('✅ E-Seal sync result:', responseData);
      console.log('🔄 Sync status:', isBeacukaiSuccess ? 'SUCCESS' : 'FAILED');
      
    } catch (syncError) {
      console.error('❌ Error syncing E-Seal with Beacukai:', syncError);
      
      // If sync fails, set isSync to false
      await prisma.eSeal.update({
        where: { id: eseal.id },
        data: {
          isSync: false,
          beacukaiResponseLog: { error: syncError instanceof Error ? syncError.message : 'Unknown error' } as any
        }
      });
    }

    return c.json({
      success: true,
      data: eseal,
      message: 'E-Seal created successfully',
    });
  } catch (error) {
    console.error('Error creating E-Seal:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create E-Seal',
    }, 500);
  }
})

// Update E-Seal
.put('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();

    // Check if E-Seal exists
    const existingESeal = await prisma.eSeal.findUnique({
      where: { id }
    });

    if (!existingESeal) {
      return c.json({
        success: false,
        error: 'E-Seal not found',
      }, 404);
    }

    // Update E-Seal
    const updatedESeal = await prisma.eSeal.update({
      where: { id },
      data: {
        ...(body.noEseal && { noEseal: body.noEseal }),
        ...(body.noImei && { noImei: body.noImei }),
        ...(body.idVendor && { idVendor: body.idVendor }),
        ...(body.merk && { merk: body.merk }),
        ...(body.model && { model: body.model }),
        ...(body.tipe && { tipe: body.tipe }),
        ...(body.alamatAsal && { alamatAsal: body.alamatAsal }),
        ...(body.alamatTujuan && { alamatTujuan: body.alamatTujuan }),
        ...(body.lokasiAsal && { lokasiAsal: body.lokasiAsal }),
        ...(body.lokasiTujuan && { lokasiTujuan: body.lokasiTujuan }),
        ...(body.latitudeAsal && { latitudeAsal: body.latitudeAsal }),
        ...(body.longitudeAsal && { longitudeAsal: body.longitudeAsal }),
        ...(body.latitudeTujuan && { latitudeTujuan: body.latitudeTujuan }),
        ...(body.longitudeTujuan && { longitudeTujuan: body.longitudeTujuan }),
        ...(body.noPolisi && { noPolisi: body.noPolisi }),
        ...(body.ukKontainer && { ukKontainer: body.ukKontainer }),
        ...(body.jnsKontainer && { jnsKontainer: body.jnsKontainer }),
        ...(body.noKontainer && { noKontainer: body.noKontainer }),
        ...(body.namaDriver && { namaDriver: body.namaDriver }),
        ...(body.nomorTeleponDriver && { nomorTeleponDriver: body.nomorTeleponDriver }),
        ...(body.nomorAJU && { nomorAJU: body.nomorAJU }),
        ...(body.statusValidasi && { statusValidasi: body.statusValidasi }),
        ...(body.status && { status: body.status }),
      },
      include: {
        dokumen: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    return c.json({
      success: true,
      data: updatedESeal,
      message: 'E-Seal updated successfully',
    });
  } catch (error) {
    console.error('Error updating E-Seal:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update E-Seal',
    }, 500);
  }
})

// Delete E-Seal
.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // Check if E-Seal exists
    const existingESeal = await prisma.eSeal.findUnique({
      where: { id }
    });

    if (!existingESeal) {
      return c.json({
        success: false,
        error: 'E-Seal not found',
      }, 404);
    }

    // Delete E-Seal (cascade will delete related records)
    await prisma.eSeal.delete({
      where: { id }
    });

    return c.json({
      success: true,
      message: 'E-Seal deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting E-Seal:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete E-Seal',
    }, 500);
  }
})
