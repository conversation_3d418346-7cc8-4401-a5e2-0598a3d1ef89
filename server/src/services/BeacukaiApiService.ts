import { PrismaClient } from '@prisma/client';
import { BeacukaiSessionService } from './BeacukaiSessionService';
import { BeacukaiApiLogger } from './BeacukaiApiLogger';
import { getBeacukaiConfig } from '../config/beacukai';

// Types for API requests/responses
export interface BeacukaiESealAddRequest {
  idVendor: string;
  merk: string;
  model: string;
  noImei: string;
  tipe: string;
  token: string;
}

export interface BeacukaiTrackingStartRequest {
  alamatAsal: string;
  alamatTujuan: string;
  idVendor: string;
  jnsKontainer: string;
  latitudeAsal: string;
  latitudeTujuan: string;
  lokasiAsal: string;
  lokasiTujuan: string;
  longitudeAsal: string;
  longitudeTujuan: string;
  noImei: string;
  noEseal: string;
  noKontainer: string;
  noPolisi: string;
  token: string;
  ukKontainer: string;
  namaDriver: string;
  nomorTeleponDriver: string;
  dokumen: Array<{
    jenisMuat: string;
    jumlahKontainer: string;
    kodeDokumen: string;
    kodeKantor: string;
    nomorAju: string;
    nomorDokumen: string;
    tanggalDokumen: string;
  }>;
}

export interface BeacukaiUpdatePositionRequest {
  address: string;
  altitude: string;
  battery: string;
  dayaAki: string;
  event: string;
  idVendor: string;
  kota: string;
  latitude: string;
  longitude: string;
  noImei: string;
  noEseal: string;
  provinsi: string;
  speed: string;
  token: string;
}

export interface BeacukaiApiResponse {
  status: 'success' | 'error';
  message: string;
  item?: any;
}

export interface BeacukaiDokumenPabeanResponse extends BeacukaiApiResponse {
  item?: {
    nomorAju: string;
    kodeDokumen: string;
    nomorDaftar: string;
    tanggalDaftar: string;
    kodeKantor: string;
    namaKantor: string;
    kodeTps: string;
    namaGudang: string;
    idPengusaha: string;
    namaPengusaha: string;
    uraian: string;
    kontainer: Array<{
      nomorKontainer: string;
      nomorSegel: string;
    }>;
  };
}

export class BeacukaiApiService {
  private sessionService: BeacukaiSessionService;
  private logger: BeacukaiApiLogger;
  private config: ReturnType<typeof getBeacukaiConfig>;

  constructor(prisma: PrismaClient) {
    this.sessionService = new BeacukaiSessionService(prisma);
    this.logger = new BeacukaiApiLogger(prisma);
    this.config = getBeacukaiConfig();
  }

  /**
   * Add E-Seal to Beacukai system using direct API call
   */
  async addESeal(data: Omit<BeacukaiESealAddRequest, 'token'>): Promise<BeacukaiApiResponse> {
    console.log('🚀 Adding E-Seal to Beacukai system:', data.noImei);

    // Add vendor token for API call (using valid token from documentation)
    const requestData = {
      ...data,
      token: '919253c8-d0e1-4780-89d0-e91f77e89855' // Valid vendor token from Beacukai documentation
    };

    try {
      // Get session for JWT token and cookies
      const session = await this.sessionService.getValidSession();
      const jwtToken = (session as any).jwtToken;
      
      // Prepare request headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'E-Seal-Monitor/1.0'
      };
      
      // Add cookies to headers
      if (session.cookies.length > 0) {
        const cookieValues = session.cookies.map(cookie => {
          const cookiePart = cookie?.split(';')[0]?.trim();
          return cookiePart;
        }).filter(Boolean);
        
        if (cookieValues.length > 0) {
          requestHeaders['Cookie'] = cookieValues.join('; ');
        }
      }
      
      // Direct API call to Beacukai
      const apiUrl = 'https://apisdev-gw.beacukai.go.id/tracking-eseal/eseal/add';
      console.log(`🌐 Making direct API call to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestData)
      });
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse response as JSON:', responseText);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }
      
      console.log('📡 API Response:', responseData);
      
      // Log API call
      await this.logger.logApiCall({
        requestUrl: apiUrl,
        requestBody: requestData,
        requestHeader: requestHeaders,
        status: response.status,
        responseBody: responseData,
        isSync: true
      });

      return {
        status: responseData.status === 'success' ? 'success' : 'error',
        message: responseData.message || 'E-Seal added successfully',
        item: responseData.item || responseData.data
      };
    } catch (error) {
      console.error('❌ Error adding E-Seal:', error);
      
      // Get session for headers (even for error logging)
      try {
        const session = await this.sessionService.getValidSession();
        const jwtToken = (session as any).jwtToken;
        
        // Prepare request headers for logging
        const requestHeaders: Record<string, string> = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${jwtToken}`,
          'User-Agent': 'E-Seal-Monitor/1.0'
        };
        
        // Add cookies to headers
        if (session.cookies.length > 0) {
          const cookieValues = session.cookies.map(cookie => {
            const cookiePart = cookie?.split(';')[0]?.trim();
            return cookiePart;
          }).filter(Boolean);
          
          if (cookieValues.length > 0) {
            requestHeaders['Cookie'] = cookieValues.join('; ');
          }
        }
        
        // Log failed API call with headers
        await this.logger.logApiCall({
          requestUrl: `${this.config.endpoints.esealUrl}/eseal/add`,
          requestBody: requestData,
          requestHeader: requestHeaders,
          status: 500,
          responseBody: { error: error instanceof Error ? error.message : 'Unknown error' },
          isSync: true
        });
      } catch (sessionError) {
        // Fallback logging without headers if session fails
        await this.logger.logApiCall({
          requestUrl: `${this.config.endpoints.esealUrl}/eseal/add`,
          requestBody: requestData,
          status: 500,
          responseBody: { error: error instanceof Error ? error.message : 'Unknown error' },
          isSync: true
        });
      }

      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }

  /**
   * Get dokumen pabean by AJU number using direct API call
   */
  async getDokumenPabean(nomorAju: string): Promise<BeacukaiDokumenPabeanResponse> {
    console.log('📋 Getting dokumen pabean for AJU:', nomorAju);

    try {
      // Get session for JWT token and cookies
      const session = await this.sessionService.getValidSession();
      const jwtToken = (session as any).jwtToken;
      
      // Prepare request headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'E-Seal-Monitor/1.0'
      };
      
      // Add cookies to headers
      if (session.cookies.length > 0) {
        const cookieValues = session.cookies.map(cookie => {
          const cookiePart = cookie?.split(';')[0]?.trim();
          return cookiePart;
        }).filter(Boolean);
        
        if (cookieValues.length > 0) {
          requestHeaders['Cookie'] = cookieValues.join('; ');
        }
      }
      
      // Direct API call to Beacukai
      const apiUrl = `https://apisdev-gw.beacukai.go.id/dokumen-eseal-service/eseal/get-dok-pabean?nomor_aju=${nomorAju}`;
      console.log(`🌐 Making direct API call to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: requestHeaders
      });
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse response as JSON:', responseText);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }
      
      console.log('📡 API Response:', responseData);
      
      // Log API call
      await this.logger.logApiCall({
        requestUrl: apiUrl,
        requestHeader: requestHeaders,
        status: response.status,
        responseBody: responseData,
        isSync: true
      });

      return {
        status: responseData.status === 'success' ? 'success' : 'error',
        message: responseData.message || 'Data retrieved successfully',
        item: responseData.item || responseData.data
      };
    } catch (error) {
      console.error('❌ Error getting dokumen pabean:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }

  /**
   * Start tracking for E-Seal using direct API call
   */
  async startTracking(data: Omit<BeacukaiTrackingStartRequest, 'token'>): Promise<BeacukaiApiResponse> {
    console.log('🚀 Starting tracking for E-Seal:', data.noEseal);

    // Add vendor token for API call (using valid token from documentation)
    const requestData = {
      ...data,
      token: '919253c8-d0e1-4780-89d0-e91f77e89855' // Valid vendor token from Beacukai documentation
    };

    try {
      // Get session for JWT token and cookies
      const session = await this.sessionService.getValidSession();
      const jwtToken = (session as any).jwtToken;
      
      // Prepare request headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'E-Seal-Monitor/1.0'
      };
      
      // Add cookies to headers
      if (session.cookies.length > 0) {
        const cookieValues = session.cookies.map(cookie => {
          const cookiePart = cookie?.split(';')[0]?.trim();
          return cookiePart;
        }).filter(Boolean);
        
        if (cookieValues.length > 0) {
          requestHeaders['Cookie'] = cookieValues.join('; ');
        }
      }
      
      // Direct API call to Beacukai
      const apiUrl = 'https://apisdev-gw.beacukai.go.id/tracking-eseal/tracking/start';
      console.log(`🌐 Making direct API call to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestData)
      });
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse response as JSON:', responseText);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }
      
      console.log('📡 API Response:', responseData);
      
      // Log API call
      await this.logger.logApiCall({
        requestUrl: apiUrl,
        requestBody: requestData,
        requestHeader: requestHeaders,
        status: response.status,
        responseBody: responseData,
        isSync: true
      });

      return {
        status: responseData.status === 'success' ? 'success' : 'error',
        message: responseData.message || 'Tracking started successfully',
        item: responseData.item || responseData.data
      };
    } catch (error) {
      console.error('❌ Error starting tracking:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }

  /**
   * Update E-Seal position using direct API call
   */
  async updatePosition(data: Omit<BeacukaiUpdatePositionRequest, 'token'>): Promise<BeacukaiApiResponse> {
    console.log('📍 Updating E-Seal position:', data.noEseal);

    // Add vendor token for API call
    const requestData = {
      ...data,
      token: '919253c8-d0e1-4780-89d0-e91f77e89855' // Valid vendor token from Beacukai documentation
    };

    try {
      // Get session for JWT token and cookies
      const session = await this.sessionService.getValidSession();
      const jwtToken = (session as any).jwtToken;
      
      // Prepare request headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'E-Seal-Monitor/1.0'
      };
      
      // Add cookies to headers
      if (session.cookies.length > 0) {
        const cookieValues = session.cookies.map(cookie => {
          const cookiePart = cookie?.split(';')[0]?.trim();
          return cookiePart;
        }).filter(Boolean);
        
        if (cookieValues.length > 0) {
          requestHeaders['Cookie'] = cookieValues.join('; ');
        }
      }
      
      // Direct API call to Beacukai
      const apiUrl = 'https://apisdev-gw.beacukai.go.id/position-eseal/eseal/update-position';
      console.log(`🌐 Making direct API call to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestData)
      });
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse response as JSON:', responseText);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }
      
      console.log('📡 API Response:', responseData);
      
      // Log API call
      await this.logger.logApiCall({
        requestUrl: apiUrl,
        requestBody: requestData,
        requestHeader: requestHeaders,
        status: response.status,
        responseBody: responseData,
        isSync: true
      });

      return {
        status: responseData.status === 'success' ? 'success' : 'error',
        message: responseData.message || 'Position updated successfully',
        item: responseData.item || responseData.data
      };
    } catch (error) {
      console.error('❌ Error updating position:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }

  /**
   * Stop tracking E-Seal using direct API call
   */
  async stopTracking(data: {
    alamatStop: string;
    idVendor: string;
    latitudeStop: string;
    longitudeStop: string;
    noImei: string;
    noEseal: string;
    token: string;
  }): Promise<BeacukaiApiResponse> {
    console.log('🛑 Stopping tracking for E-Seal:', data.noEseal);

    const requestData = data;

    try {
      // Get session for JWT token and cookies
      const session = await this.sessionService.getValidSession();
      const jwtToken = (session as any).jwtToken;
      
      // Prepare request headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${jwtToken}`,
        'User-Agent': 'E-Seal-Monitor/1.0'
      };
      
      // Add cookies to headers
      if (session.cookies.length > 0) {
        const cookieValues = session.cookies.map(cookie => {
          const cookiePart = cookie?.split(';')[0]?.trim();
          return cookiePart;
        }).filter(Boolean);
        
        if (cookieValues.length > 0) {
          requestHeaders['Cookie'] = cookieValues.join('; ');
        }
      }
      
      // Direct API call to Beacukai
      const apiUrl = `${this.config.endpoints.esealUrl}/tracking/stop`;
      console.log(`🌐 Making direct API call to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestData)
      });
      
      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        console.error('❌ Failed to parse response as JSON:', responseText);
        throw new Error(`API request failed: ${response.status} ${response.statusText} - Body: ${responseText}`);
      }
      
      console.log('📡 API Response:', responseData);
      
      // Log API call
      await this.logger.logApiCall({
        requestUrl: apiUrl,
        requestBody: requestData,
        requestHeader: requestHeaders,
        status: response.status,
        responseBody: responseData,
        isSync: true
      });

      const isSuccess = responseData.status === 'success' || (responseData.success === true);

      return {
        status: isSuccess ? 'success' : 'error',
        message: responseData.message || 'Tracking stopped successfully',
        item: responseData.item || responseData.data
      };
    } catch (error) {
      console.error('❌ Error stopping tracking:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }

  /**
   * Update device status using session-based authentication
   */
  async updateStatusDevice(data: {
    idVendor: string;
    noImei: string;
    status: string;
  }): Promise<BeacukaiApiResponse> {
    console.log('🔄 Updating device status:', data.noImei, 'to', data.status);

    try {
      const response = await this.sessionService.makeAuthenticatedRequest(
        '/eseal/update-status-device',
        'POST',
        data
      );

      return {
        status: response.status === 'success' ? 'success' : 'error',
        message: response.message || 'Device status updated successfully',
        item: response.item || response.data
      };
    } catch (error) {
      console.error('❌ Error updating device status:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }

  /**
   * Get tracking status using session-based authentication
   */
  async getTrackingStatus(idVendor: string, noEseal: string): Promise<BeacukaiApiResponse> {
    console.log('📊 Getting tracking status for:', noEseal);

    try {
      const response = await this.sessionService.makeAuthenticatedRequest(
        `/tracking/status?idVendor=${idVendor}&noEseal=${noEseal}`,
        'GET'
      );

      return {
        status: response.status === 'success' ? 'success' : 'error',
        message: response.message || 'Tracking status retrieved successfully',
        item: response.item || response.data
      };
    } catch (error) {
      console.error('❌ Error getting tracking status:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        item: undefined
      };
    }
  }



  /**
   * Get session info for debugging
   */
  async getSessionInfo() {
    return this.sessionService.getSessionInfo();
  }

  /**
   * Test connection to Beacukai API
   */
  async testConnection(): Promise<boolean> {
    return await this.sessionService.testConnection();
  }

  /**
   * Invalidate current session
   */
  async invalidateSession(): Promise<void> {
    return await this.sessionService.invalidateSession();
  }
}
