import { PrismaClient } from '@prisma/client';
import { GpsService } from './GpsService';
import { BeacukaiApiService } from './BeacukaiApiService';
import { randomUUID } from 'crypto';

export class CronJobService {
  private prisma: PrismaClient;
  private gpsService: GpsService;
  private beacukaiService: BeacukaiApiService;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.gpsService = new GpsService(prisma);
    this.beacukaiService = new BeacukaiApiService(prisma);
  }

  /**
   * Auto-create tracking sessions for E-Seals without active sessions
   */
  private async autoCreateTrackingSessions(): Promise<void> {
    try {
      // Get all ACTIVE E-Seals that don't have active tracking sessions
       const esealsWithoutActiveSessions = await this.prisma.eSeal.findMany({
         where: {
           status: 'ACTIVE', // Only include active E-Seals
           trackingSessions: {
             none: {
               sessionStatus: 'ACTIVE'
             }
           }
         }
       });

       console.log(`🔄 Auto-creating tracking sessions for ${esealsWithoutActiveSessions.length} E-Seals`);

       // Create tracking sessions for each E-Seal
        for (const eseal of esealsWithoutActiveSessions) {
          await this.prisma.trackingSession.create({
            data: {
              id: randomUUID(),
              esealId: eseal.id,
              sessionStatus: 'ACTIVE',
              startedAt: new Date(),
              totalUpdates: 0,
              updatedAt: new Date()
            }
          });
          console.log(`✅ Created auto tracking session for E-Seal: ${eseal.noEseal}`);
        }
    } catch (error) {
      console.error('❌ Error auto-creating tracking sessions:', error);
    }
  }

  /**
   * Main cron job that runs every 5 minutes to update positions
   */
  async runPositionUpdateJob(): Promise<void> {
    const startTime = Date.now();
    const jobName = 'position-update';
    
    console.log('🔄 Starting position update cron job...');
    
    try {
      // Log job start
      const jobLog = await this.prisma.cronJobLog.create({
        data: {
          id: randomUUID(),
          jobName,
          status: 'RUNNING',
          message: 'Position update job started'
        }
      });

      // Auto-create tracking sessions for all E-Seals without active sessions
      await this.autoCreateTrackingSessions();

      // Get all active tracking sessions
      const activeSessions = await this.prisma.trackingSession.findMany({
        where: {
          sessionStatus: 'ACTIVE'
        },
        include: {
          eseal: true
        }
      });

      console.log(`📍 Found ${activeSessions.length} active tracking sessions`);

      let devicesProcessed = 0;
      let errorsCount = 0;
      const errors: string[] = [];

      // Get all GPS devices data
      const gpsDevices = await this.gpsService.getGpsPositionData();
      console.log(`📍 Found ${gpsDevices.length} GPS devices`);

      // Process each GPS device and update tracking logs
      for (const device of gpsDevices) {
        try {
          await this.processGpsDeviceUpdate(device);
          devicesProcessed++;
        } catch (error) {
          errorsCount++;
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`${device.deviceId}: ${errorMsg}`);
          console.error(`❌ Error processing device ${device.deviceId}:`, error);
        }
      }

      // Update tracking session stats for active sessions
      for (const session of activeSessions) {
        try {
          await this.prisma.trackingSession.update({
            where: { id: session.id },
            data: {
              totalUpdates: { increment: 1 },
              lastUpdateAt: new Date()
            }
          });
        } catch (error) {
          console.error(`❌ Error updating session ${session.id}:`, error);
        }
      }

      const executionTime = Date.now() - startTime;
      
      // Update job log with results
      await this.prisma.cronJobLog.update({
        where: { id: jobLog.id },
        data: {
          status: errorsCount === 0 ? 'SUCCESS' : 'ERROR',
          message: `Processed ${devicesProcessed} devices, ${errorsCount} errors`,
          executionTime,
          devicesProcessed,
          errorsCount,
          jobData: {
            activeSessions: activeSessions.length,
            errors: errors.slice(0, 10) // Limit error details
          }
        }
      });

      console.log(`✅ Position update job completed in ${executionTime}ms`);
      console.log(`📊 Stats: ${devicesProcessed} processed, ${errorsCount} errors`);
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      
      console.error('💥 Position update job failed:', error);
      
      // Log job failure
      await this.prisma.cronJobLog.create({
        data: {
          id: randomUUID(),
          jobName,
          status: 'ERROR',
          message: `Job failed: ${errorMsg}`,
          executionTime,
          errorsCount: 1,
          jobData: { error: errorMsg }
        }
      });
    }
  }

  /**
   * Process GPS device update, send to Beacukai, and save to tracking log
   */
  private async processGpsDeviceUpdate(device: any): Promise<void> {
    console.log(`📍 Processing GPS device: ${device.deviceId}`);
    
    try {
      // Find matching ACTIVE E-Seal by device ID (which is noImei)
      const eseal = await this.prisma.eSeal.findFirst({
        where: {
          noImei: device.deviceId,
          status: 'ACTIVE'
        }
      });

      if (!eseal) {
        // This is not an error, just a GPS device that isn't currently tracking an active E-Seal
        console.log(`- No active E-Seal found for device ID: ${device.deviceId}. Skipping.`);
        return;
      }

      console.log(`Found active E-Seal: ${eseal.noEseal} for device: ${device.deviceId}`);

      // 1. Prepare data for Beacukai's update-position endpoint
      const positionData = {
        address: `GPS: ${device.lat}, ${device.lng}`, // Placeholder, reverse geocoding can be added later
        altitude: '0', // Not available from GPS API
        battery: device.devBatteryPCT?.toString() || '0',
        dayaAki: device.devBatteryPCT?.toString() || '0', // Assuming same as battery
        event: device.lockStatus?.toString() || '0', // Mapping lock status to event
        idVendor: eseal.idVendor,
        kota: '', // Placeholder
        latitude: device.lat.toString(),
        longitude: device.lng.toString(),
        noImei: eseal.noImei,
        noEseal: eseal.noEseal,
        provinsi: '', // Placeholder
        speed: device.speed?.toString() || '0',
        token: '' // Add token if needed
      };

      // 2. Send position update to Beacukai API
      console.log(`📤 Sending position update to Beacukai for E-Seal: ${eseal.noEseal}`);
      const beacukaiResult = await this.beacukaiService.updatePosition(positionData);

      // 3. Create tracking log entry with all relevant data
      await this.prisma.trackingLog.create({
        data: {
          id: randomUUID(),
          esealId: eseal.id,
          latitude: positionData.latitude,
          longitude: positionData.longitude,
          address: positionData.address,
          altitude: positionData.altitude,
          speed: positionData.speed,
          battery: positionData.battery,
          dayaAki: positionData.dayaAki,
          event: positionData.event,
          kota: positionData.kota,
          provinsi: positionData.provinsi,
          gpsDeviceId: device.deviceId,
          gpsTime: device.gpsTime,
          direction: device.direction?.toString(),
          mileage: device.mileage?.toString(),
          online: device.online,
          locate: device.locate,
          apiResponse: beacukaiResult as any, // Save Beacukai's response
          beacukaiStatus: beacukaiResult.status,
        }
      });
      
      // 4. Update the local device status for real-time monitoring
      await this.updateDeviceStatus(eseal.id, positionData, beacukaiResult.status === 'success');

      console.log(`✅ Successfully processed and logged position for E-Seal ${eseal.noEseal}`);
      
    } catch (error) {
      console.error(`❌ Failed to process GPS device ${device.deviceId}:`, error);
      // We throw the error so the main job runner can catch and log it.
      throw error;
    }
  }

  /**
   * Update device status in database
   */
  private async updateDeviceStatus(esealId: string, positionData: any, beacukaiSuccess: boolean): Promise<void> {
    await this.prisma.deviceStatus.upsert({
      where: { esealId },
      update: {
        lastKnownLat: positionData.latitude,
        lastKnownLng: positionData.longitude,
        lastKnownAddress: positionData.address,
        gpsOnline: true,
        lastGpsUpdate: new Date(),
        batteryLevel: positionData.battery,
        updatedAt: new Date()
      },
      create: {
        id: randomUUID(),
        esealId,
        currentStatus: 'ACTIVE',
        lastKnownLat: positionData.latitude,
        lastKnownLng: positionData.longitude,
        lastKnownAddress: positionData.address,
        gpsOnline: true,
        lastGpsUpdate: new Date(),
        batteryLevel: positionData.battery,
        registeredWithBeacukai: beacukaiSuccess,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Get cron job execution history
   */
  async getJobHistory(jobName?: string, limit: number = 50): Promise<any[]> {
    return await this.prisma.cronJobLog.findMany({
      where: jobName ? { jobName } : undefined,
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  /**
   * Clean up old job logs (keep last 1000 entries)
   */
  async cleanupJobLogs(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30); // Keep 30 days

    const deleted = await this.prisma.cronJobLog.deleteMany({
      where: {
        createdAt: { lt: cutoffDate }
      }
    });

    console.log(`🧹 Cleaned up ${deleted.count} old job logs`);
  }
}
