import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search, Plus, X, Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { useESealData, type ESealData } from '../../../hooks/useESealData';

const StartTrackingModal = ({ isOpen, onClose, onSubmit, eseals, loading }: any) => {
  const [formData, setFormData] = useState({
    noEseal: '',
    alamatAsal: '',
    alamatTujuan: '',
    latitudeAsal: '',
    longitudeAsal: '',
    latitudeTujuan: '',
    longitudeTujuan: '',
    noKontainer: '',
    ukKontainer: '',
    jnsKontainer: '',
    noPolisi: '',
    namaDriver: '',
    nomorTeleponDriver: '',
    nomorAju: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Mulai Tracking Baru</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="w-6 h-6" />
            </button>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Select onValueChange={(v) => setFormData({ ...formData, noEseal: v })}>
              <SelectTrigger><SelectValue placeholder="Pilih E-Seal" /></SelectTrigger>
              <SelectContent>
                {eseals.map((eseal: ESealData) => (
                  <SelectItem key={eseal.id} value={eseal.noEseal!}>
                    {eseal.noEseal} ({eseal.noImei})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input placeholder="Alamat Asal" onChange={(e) => setFormData({ ...formData, alamatAsal: e.target.value })} />
            <Input placeholder="Alamat Tujuan" onChange={(e) => setFormData({ ...formData, alamatTujuan: e.target.value })} />
            <div className="grid grid-cols-2 gap-4">
              <Input placeholder="Latitude Asal" onChange={(e) => setFormData({ ...formData, latitudeAsal: e.target.value })} />
              <Input placeholder="Longitude Asal" onChange={(e) => setFormData({ ...formData, longitudeAsal: e.target.value })} />
              <Input placeholder="Latitude Tujuan" onChange={(e) => setFormData({ ...formData, latitudeTujuan: e.target.value })} />
              <Input placeholder="Longitude Tujuan" onChange={(e) => setFormData({ ...formData, longitudeTujuan: e.target.value })} />
            </div>
            <Input placeholder="No Kontainer" onChange={(e) => setFormData({ ...formData, noKontainer: e.target.value })} />
            <Input placeholder="Ukuran Kontainer" onChange={(e) => setFormData({ ...formData, ukKontainer: e.target.value })} />
            <Input placeholder="Jenis Kontainer" onChange={(e) => setFormData({ ...formData, jnsKontainer: e.target.value })} />
            <Input placeholder="No Polisi" onChange={(e) => setFormData({ ...formData, noPolisi: e.target.value })} />
            <Input placeholder="Nama Driver" onChange={(e) => setFormData({ ...formData, namaDriver: e.target.value })} />
            <Input placeholder="Telepon Driver" onChange={(e) => setFormData({ ...formData, nomorTeleponDriver: e.target.value })} />
            <Input placeholder="Nomor AJU" onChange={(e) => setFormData({ ...formData, nomorAju: e.target.value })} />
            <div className="flex justify-end gap-2">
              <Button type="button" variant="ghost" onClick={onClose}>Batal</Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Mulai Tracking
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};


const StartStop: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [stoppingEsealId, setStoppingEsealId] = useState<string | null>(null);

  const { data: esealData, loading, error, refetch } = useESealData({
    page: 1,
    limit: 100, // Fetch all for selection
    organizationSlug: slug,
  });

  const activeEseals = esealData?.filter(e => e.status === 'ACTIVE') || [];
  const inactiveEseals = esealData?.filter(e => e.status === 'INACTIVE') || [];

  const handleStartTracking = async (formData: any) => {
    setIsSubmitting(true);
    const selectedEseal = esealData?.find(e => e.noEseal === formData.noEseal);
    if (!selectedEseal) {
      alert('E-Seal tidak ditemukan');
      setIsSubmitting(false);
      return;
    }

    const payload = {
      ...formData,
      noImei: selectedEseal.noImei,
      idVendor: selectedEseal.idVendor,
      token: '', // Add token if needed
      organizationSlug: slug,
    };

    try {
      const response = await fetch('/api/beacukai/tracking/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const result = await response.json();
      if (!response.ok) throw new Error(result.message || 'Gagal memulai tracking');
      alert('Tracking berhasil dimulai');
      setIsModalOpen(false);
      refetch();
    } catch (err: any) {
      alert(`Error: ${err.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStopTracking = async (eseal: ESealData) => {
    const alamat = prompt(`Masukkan alamat pemberhentian untuk E-Seal ${eseal.noEseal}:`);
    if (!alamat) {
      alert('Alamat pemberhentian tidak boleh kosong.');
      return;
    }

    setStoppingEsealId(eseal.id);

    const payload = {
      noEseal: eseal.noEseal,
      noImei: eseal.noImei,
      idVendor: eseal.idVendor,
      alamatStop: alamat,
      latitudeStop: '0', // Placeholder, ideally get from browser GPS
      longitudeStop: '0', // Placeholder, ideally get from browser GPS
      token: '', // TODO: figure out where to get the token
      organizationSlug: slug,
    };

    try {
      const response = await fetch('/api/beacukai/tracking/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Gagal menghentikan tracking');
      }
      alert('Tracking berhasil dihentikan');
      refetch();
    } catch (err: any) {
      alert(`Terjadi kesalahan: ${err.message}`);
    } finally {
      setStoppingEsealId(null);
    }
  };


  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Start/Stop</h1>
      </div>

      <div className="flex justify-end items-center gap-3 bg-white p-4 rounded-lg border">
        <Button onClick={() => setIsModalOpen(true)} className="bg-slate-800 hover:bg-slate-700 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Mulai Tracking
        </Button>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nomor E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor AJU</TableHead>
                <TableHead className="font-semibold text-slate-700">Status</TableHead>
                <TableHead className="font-semibold text-slate-700">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8"><Loader2 className="mx-auto h-6 w-6 animate-spin" /></TableCell></TableRow>
              ) : error ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8 text-red-500">{error}</TableCell></TableRow>
              ) : activeEseals.length === 0 ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8 text-gray-500">Tidak ada tracking yang aktif.</TableCell></TableRow>
              ) : (
                activeEseals.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.noEseal}</TableCell>
                    <TableCell>{item.noImei}</TableCell>
                    <TableCell>{item.nomorAJU || '-'}</TableCell>
                    <TableCell><span className="text-green-600 font-semibold">Aktif</span></TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleStopTracking(item)}
                        disabled={stoppingEsealId === item.id}
                      >
                        {stoppingEsealId === item.id ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          'Stop'
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <StartTrackingModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleStartTracking}
        eseals={inactiveEseals}
        loading={isSubmitting}
      />
    </div>
  );
};

export default StartStop;
