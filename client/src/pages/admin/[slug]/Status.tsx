import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Loader2 } from 'lucide-react';

interface TrackingStatus {
  start: string;
  updatePosition: number;
  stop: string;
}

const Status: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [noEseal, setNoEseal] = useState('');
  const [idVendor, setIdVendor] = useState('');
  const [token, setToken] = useState('');
  const [status, setStatus] = useState<TrackingStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFetchStatus = async () => {
    if (!noEseal || !idVendor) {
      setError('Nomor E-Seal dan ID Vendor harus diisi.');
      return;
    }
    setLoading(true);
    setError(null);
    setStatus(null);

    try {
      const response = await fetch(`/api/beacukai/tracking/status?noEseal=${noEseal}&idVendor=${idVendor}&token=${token}&organizationSlug=${slug}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Gagal mengambil status tracking.');
      }

      setStatus(result.item);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Status</h1>
      </div>

      <div className="bg-white p-6 rounded-lg border space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1">
            <label htmlFor="idVendor" className="text-sm font-medium text-gray-700">
              ID Vendor <span className="text-red-500">*</span>
            </label>
            <Input id="idVendor" placeholder="Masukkan ID Vendor" value={idVendor} onChange={(e) => setIdVendor(e.target.value)} />
          </div>
          <div className="space-y-1">
            <label htmlFor="nomorEseal" className="text-sm font-medium text-gray-700">
              Nomor E-Seal <span className="text-red-500">*</span>
            </label>
            <Input id="nomorEseal" placeholder="Masukkan Nomor E-Seal" value={noEseal} onChange={(e) => setNoEseal(e.target.value)} />
          </div>
        </div>
        <div className="space-y-1">
          <label htmlFor="token" className="text-sm font-medium text-gray-700">
            Token <span className="text-gray-500">(opsional)</span>
          </label>
          <Input id="token" placeholder="Masukkan Token" value={token} onChange={(e) => setToken(e.target.value)} />
        </div>
        <p className="text-xs text-gray-500">* Kolom wajib diisi</p>
        <div className="flex justify-end">
          <Button onClick={handleFetchStatus} disabled={loading} className="bg-slate-800 hover:bg-slate-700 text-white">
            {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Ambil Data'}
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Nomor E-Seal: {noEseal || '-'}</h2>
        </div>
        {loading ? (
          <div className="p-8 text-center"><Loader2 className="mx-auto h-6 w-6 animate-spin" /></div>
        ) : error ? (
          <div className="p-8 text-center text-red-500">{error}</div>
        ) : status ? (
          <div className="flex flex-col md:flex-row">
            <div className="flex-1 p-4 text-center border-b md:border-b-0 md:border-r">
              <h3 className="font-semibold">Start</h3>
              <p className={status.start === 'success' ? 'text-green-600' : 'text-red-600'}>{status.start}</p>
            </div>
            <div className="flex-1 p-4 text-center border-b md:border-b-0 md:border-r">
              <h3 className="font-semibold">Update Position</h3>
              <p>{status.updatePosition} kali</p>
            </div>
            <div className="flex-1 p-4 text-center">
              <h3 className="font-semibold">Stop</h3>
              <p className={status.stop === 'success' ? 'text-green-600' : 'text-red-600'}>{status.stop}</p>
            </div>
          </div>
        ) : (
          <div className="p-8 text-center text-gray-500">
            Data akan muncul di sini.
          </div>
        )}
      </div>
    </div>
  );
};

export default Status;
