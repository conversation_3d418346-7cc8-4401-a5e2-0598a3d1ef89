import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Download, Save, Upload, Filter, List, Columns } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { useESealData } from '../../../hooks/useESealData';

const Laporan: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [selectedEseal, setSelectedEseal] = useState('');
  const [date, setDate] = useState('');
  const [exportAddress, setExportAddress] = useState(false);

  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000, // fetch all for selection
    organizationSlug: slug,
  });

  const handleDownload = () => {
    if (!selectedEseal || !date) {
      alert('Silakan pilih E-Seal dan tanggal terlebih dahulu.');
      return;
    }
    // Dummy download logic
    alert(`Downloading report for ${selectedEseal} on ${date}, export with address: ${exportAddress}`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Laporan</h1>
      </div>

      <div className="bg-white p-4 rounded-lg border space-y-4">
        <div className="flex flex-wrap items-center gap-4">
          <Select onValueChange={setSelectedEseal}>
            <SelectTrigger className="w-full sm:w-auto">
              <SelectValue placeholder="-PILIH NOMOR E-SEAL" />
            </SelectTrigger>
            <SelectContent>
              {esealData?.map(eseal => (
                <SelectItem key={eseal.id} value={eseal.noEseal!}>
                  {eseal.noEseal}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Input type="date" className="w-full sm:w-auto" value={date} onChange={e => setDate(e.target.value)} />
          <Button onClick={handleDownload} className="bg-slate-800 hover:bg-slate-700 text-white">
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
          <div className="flex items-center space-x-2">
            <input type="checkbox" id="exportAddress" checked={exportAddress} onChange={e => setExportAddress(e.target.checked)} />
            <label htmlFor="exportAddress" className="text-sm">Export with Address</label>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">No</TableHead>
                <TableHead className="font-semibold text-slate-700">No E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">No Aju</TableHead>
                <TableHead className="font-semibold text-slate-700">No IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Event</TableHead>
                <TableHead className="font-semibold text-slate-700">Count</TableHead>
                <TableHead className="font-semibold text-slate-700">Duration</TableHead>
                <TableHead className="font-semibold text-slate-700">Mileage</TableHead>
                <TableHead className="font-semibold text-slate-700">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-slate-700">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                  Data Tidak Ditemukan
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Laporan;
