import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Input } from '../../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search } from 'lucide-react';
import MapTiler from '../../../components/map/MapTiler';
import { useESealData } from '../../../hooks/useESealData';

const Monitoring: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [search, setSearch] = useState('');
  const [selectedEseals, setSelectedEseals] = useState<string[]>([]);

  const { data: esealData } = useESealData({
    page: 1,
    limit: 1000,
    organizationSlug: slug,
    search,
  });

  const handleSelectEseal = (id: string) => {
    setSelectedEseals(prev =>
      prev.includes(id) ? prev.filter(esealId => esealId !== id) : [...prev, id]
    );
  };

  const monitoredEseals = esealData?.filter(e => selectedEseals.includes(e.id)) || [];
  const staticLocation = { lat: -6.2088, lng: 106.8456, address: 'Jakarta, Indonesia' };

  return (
    <div className="space-y-6 h-full flex flex-col">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Monitoring</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">
        {/* Left Panel */}
        <div className="lg:col-span-1 bg-white p-4 rounded-lg border flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Cari E-Seal..." className="pl-10" value={search} onChange={e => setSearch(e.target.value)} />
          </div>
          <div className="flex-grow space-y-2 overflow-y-auto">
            {esealData?.map(eseal => (
              <div key={eseal.id} className="flex items-center space-x-2">
                <input type="checkbox" id={eseal.id} checked={selectedEseals.includes(eseal.id)} onChange={() => handleSelectEseal(eseal.id)} />
                <label htmlFor={eseal.id} className="text-sm">{eseal.noEseal}</label>
                <span className={`text-xs ${eseal.status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'}`}>
                  {eseal.status === 'ACTIVE' ? 'ONLINE' : 'OFFLINE'}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Right Panel - Map */}
        <div className="lg:col-span-2 bg-white p-4 rounded-lg border">
          <MapTiler originLocation={staticLocation} interactive={false} />
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">No</TableHead>
                <TableHead className="font-semibold text-slate-700">No E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">No IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {monitoredEseals.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                    Pilih E-Seal untuk dimonitor.
                  </TableCell>
                </TableRow>
              ) : (
                monitoredEseals.map((eseal, index) => (
                  <TableRow key={eseal.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{eseal.noEseal}</TableCell>
                    <TableCell>{eseal.noImei}</TableCell>
                    <TableCell>Jalan Pantura, Kendal, Jawa Tengah</TableCell> 
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Monitoring;
